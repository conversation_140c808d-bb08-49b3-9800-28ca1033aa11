package com.stepup.springrobot.controller;

import com.stepup.springrobot.service.AudioProcessingService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

@Slf4j
@RestController
@CrossOrigin("*")
@RequestMapping("api/robot/conversation")
@Api(tags = "Robot Conversation", description = "APIs for robot conversation")
public class RobotConversationController {
    private final AudioProcessingService audioProcessingService;

    @Autowired
    public RobotConversationController(
            AudioProcessingService audioProcessingService) {
        this.audioProcessingService = audioProcessingService;
    }

    @PostMapping(value = "/sessions/{sessionId}/process-audio", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ApiOperation(value = "Process audio for a specific WebSocket session", notes = "Converts audio to text and sends result through the WebSocket")
    public ResponseEntity<?> processAudioForSession(
            @PathVariable String sessionId,
            @RequestParam("audioFile") MultipartFile audioFile) {

        log.info("Processing audio for session {}: {}, size: {} bytes",
                sessionId,
                audioFile.getOriginalFilename(),
                audioFile.getSize());

        try {
            // Xử lý file audio và gửi kết quả qua WebSocket
            return audioProcessingService.processAudioForSession(sessionId, audioFile);

        } catch (Exception e) {
            log.error("Error processing audio for session {}: {}", sessionId, e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of(
                            "status", "error",
                            "message", "Error processing audio: " + e.getMessage()
                    ));
        }
    }
}
