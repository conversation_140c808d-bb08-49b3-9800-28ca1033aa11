package com.stepup.springrobot.dto.llm;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.stepup.springrobot.dto.chat.LlmMoodDetailDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class LlmTextDTO {
    private String text;

    private String audio;

    private List<LlmMoodDetailDTO> moods;

    private String image;

    private String video;

    @JsonProperty("voice_speed")
    private Double voiceSpeed;

    @JsonProperty("text_viewer")
    private String textViewer;

    private Integer volume;
} 