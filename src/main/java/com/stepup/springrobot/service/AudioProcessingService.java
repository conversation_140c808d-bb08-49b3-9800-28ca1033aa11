package com.stepup.springrobot.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.stepup.springrobot.dto.UserActiveRecordDTO;
import com.stepup.springrobot.dto.recognize.InternalAsrDTO;
import com.stepup.springrobot.dto.recognize.ResultAsrDTO;
import com.stepup.springrobot.exception.business.audio.AudioErrorException;
import com.stepup.springrobot.security.JwtService;
import com.stepup.springrobot.service.speech.AsrService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

@Service
@Slf4j
public class AudioProcessingService extends CommonService {
    private final AsrService asrService;

    @Autowired
    public AudioProcessingService(
            ObjectMapper objectMapper, UploadFileToS3 uploadFileToS3, JwtService jwtService,
            SlackWarningSystemService slackWarningSystemService,
            AsrService asrService) {
        super(objectMapper, uploadFileToS3, jwtService, slackWarningSystemService);
        this.asrService = asrService;
    }

    /**
     * Process audio file and send result to a specific WebSocket session
     *
     * @param sessionId The ID of the WebSocket session to send results to
     * @param audioFile The audio file to process
     * @return ResponseEntity containing processed data and status
     * @throws IOException If there's an error processing the file
     */
    public ResponseEntity<?> processAudioForSession(String sessionId, MultipartFile audioFile) {
        log.info("Processing audio file for session {}: {}", sessionId, audioFile.getOriginalFilename());

        try {

            File file = convertMultiPartFileToFile(audioFile, "", true);
            //tmp
            if (file == null) {
                throw new AudioErrorException();
            }
            // Convert audio to text using ASR service
            JsonNode asrResult = asrService.getResponseAsrSpeechToText(file);

            InternalAsrDTO asrResultDTO = objectMapper.convertValue(asrResult, new TypeReference<>() {
            });

            ResultAsrDTO result = asrResultDTO.getResult();


            if (result == null) {
                Map<String, String> errorResponse = new HashMap<>();
                errorResponse.put("error", "Failed to process audio file");
                errorResponse.put("status", "error");
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
            }

            UserActiveRecordDTO response = UserActiveRecordDTO.builder()
                    .type("ACTIVE_RECORD")
                    .text(result.getText())
                    .audioUrl(result.getAudioUrl())
                    .build();

            file.delete();
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Error processing audio for session {}: {}", sessionId, e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error processing audio for session {}: {}\", sessionId, e.getMessage() - " + e.getMessage());
        }
    }
} 