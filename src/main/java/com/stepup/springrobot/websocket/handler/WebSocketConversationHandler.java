package com.stepup.springrobot.websocket.handler;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.stepup.springrobot.service.AIRobotConversationService;
import com.stepup.springrobot.websocket.service.BaseConversationSessionService;
import com.stepup.springrobot.websocket.service.WebConversationSessionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.BinaryMessage;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.handler.BinaryWebSocketHandler;

import java.io.EOFException;
import java.nio.ByteBuffer;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Component
public class WebSocketConversationHandler extends BinaryWebSocketHandler {
    @Value("${conversation_timeout_minutes}")
    private long conversationTimeoutMinutes;

    @Value("${audio_socket_host}")
    private String socketHost;

    @Value("${is_log_audio_chunk}")
    private boolean isLogAudioChunk;

    private final Map<WebSocketSession, BaseConversationSessionService> sessionHandlers = new ConcurrentHashMap<>();

    private final ObjectMapper objectMapper;

    private final AIRobotConversationService aiRobotConversationService;


    public WebSocketConversationHandler(ObjectMapper objectMapper,
                                        AIRobotConversationService aiRobotConversationService) {
        this.objectMapper = objectMapper;
        this.aiRobotConversationService = aiRobotConversationService;
    }

    @Override
    public void afterConnectionEstablished(WebSocketSession session) {
        log.info("(Robot - BE) WebSocket connection established for session: {}", session.getId());
        log.info("User ip address: {}", session.getRemoteAddress());
        log.info("Params: {}", session.getAttributes());

        // Initialize the SpeechSessionHandler, which includes SpeechClient and other resources
        BaseConversationSessionService handler = new WebConversationSessionService(session, objectMapper, aiRobotConversationService, conversationTimeoutMinutes, socketHost, "web_mvp");

        handler.initializeSessionConfig();

        // Store the handler associated with the session
        sessionHandlers.put(session, handler);
    }

    @Override
    protected void handleBinaryMessage(WebSocketSession session, BinaryMessage message) {
        ByteBuffer payload = message.getPayload();
        BaseConversationSessionService handler = sessionHandlers.get(session);
        if (isLogAudioChunk) {
            log.info("(Robot - BE) Received audio chunk: {}", payload.array());
        }

        if (handler != null) {
            handler.handleAudioData(payload);
        }
    }

    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus status) {
        log.info("(Robot - BE) WebSocket connection closed: {}, status:{}", session.getId(), status);

        BaseConversationSessionService handler = sessionHandlers.remove(session);
        if (handler != null) {
            handler.close();
        }
    }

    @Override
    public void handleTransportError(WebSocketSession session, Throwable exception) {
        boolean isClientError = exception instanceof EOFException ||
                exception.getMessage().contains("Connection reset by peer");
        log.error("(Robot - BE) WebSocket error: {}, session: {}, error: {}",
                isClientError ? "client-side" : "server-side",
                session.getId(),
                exception.getMessage());

        BaseConversationSessionService handler = sessionHandlers.remove(session);
        if (handler != null) {
            handler.close();
        }

        exception.printStackTrace();
    }
}
