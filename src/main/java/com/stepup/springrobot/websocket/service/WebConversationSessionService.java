package com.stepup.springrobot.websocket.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.stepup.springrobot.service.AIRobotConversationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.socket.WebSocketSession;

import javax.sound.sampled.AudioFileFormat;
import javax.sound.sampled.AudioFormat;
import javax.sound.sampled.AudioInputStream;
import javax.sound.sampled.AudioSystem;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;

@Slf4j
public class WebConversationSessionService extends BaseConversationSessionService {
    public WebConversationSessionService(WebSocketSession session, ObjectMapper objectMapper, AIRobotConversationService aiRobotConversationService, long conversationTimeoutMinutes, String socketHost, String userId) {
        super(session, objectMapper, aiRobotConversationService, conversationTimeoutMinutes, socketHost, userId, null);
    }

    @Override
    protected File convertToWavFile(byte[] audioData, String fileName) {
        try {
            // Create audio format for 32-bit float
            AudioFormat audioFormat = new AudioFormat(
                    AudioFormat.Encoding.PCM_FLOAT,  // Use floating-point encoding
                    16000.0F,    // Sample rate
                    32,          // 32 bits per sample
                    1,           // Mono channel
                    4,          // Frame size (4 bytes per sample)
                    16000.0F,    // Frame rate (same as sample rate)
                    false        // Little endian (matches frontend)
            );

            // Calculate number of frames (4 bytes per sample for 32-bit float)
            long frameLength = audioData.length / 4;

            // Create audio input stream directly from the float32 data
            AudioInputStream audioInputStream = new AudioInputStream(
                    new ByteArrayInputStream(audioData),
                    audioFormat,
                    frameLength
            );

            File outputFile = new File(fileName);
            AudioSystem.write(audioInputStream, AudioFileFormat.Type.WAVE, outputFile);
            log.info("WAV file created: {}", outputFile.getAbsolutePath());

            return outputFile;
        } catch (IOException e) {
            log.error("Error converting audio data to WAV", e);
        }

        return null;
    }

    @Override
    protected byte[] convertAudioFormat(byte[] audioData) {
        // Convert 32-bit float to 16-bit PCM
        byte[] pcmData = new byte[audioData.length / 2];  // 16-bit needs half the space of 32-bit

        for (int i = 0; i < audioData.length; i += 4) {  // Process 4 bytes at a time (32-bit float)
            // Convert 4 bytes to float
            int intValue = ((audioData[i + 3] & 0xFF) << 24) |
                    ((audioData[i + 2] & 0xFF) << 16) |
                    ((audioData[i + 1] & 0xFF) << 8) |
                    (audioData[i] & 0xFF);
            float floatValue = Float.intBitsToFloat(intValue);

            // Convert float [-1.0, 1.0] to short [-32768, 32767]
            short shortValue = (short) Math.max(Short.MIN_VALUE,
                    Math.min(Short.MAX_VALUE, floatValue * 32767.0f));

            // Store as 16-bit PCM (2 bytes)
            pcmData[i / 2] = (byte) (shortValue & 0xFF);
            pcmData[i / 2 + 1] = (byte) ((shortValue >> 8) & 0xFF);
        }

        return pcmData;
    }
}
