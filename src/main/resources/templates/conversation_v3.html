<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Robot Conversation Demo</title>
    <!-- Add lamejs library for MP3 encoding -->
    <script src="https://cdn.jsdelivr.net/npm/lamejs@1.2.1/lame.min.js"></script>
    <style>
        /* ===== BASE STYLES ===== */
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
            color: #333;
        }

        h1, h2, h3 {
            color: #2c3e50;
        }

        button {
            background-color: #4a76a8;
            color: white;
            border: none;
            padding: 8px 16px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }

        button:hover {
            background-color: #3a5f8a;
        }

        button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }

        input[type="text"] {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 100%;
            box-sizing: border-box;
            margin-bottom: 10px;
        }

        /* ===== CONNECTION STATUS ===== */
        #status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            font-weight: bold;
        }

        .connected {
            background-color: rgba(0, 128, 0, 0.1);
            color: green;
        }

        .disconnected {
            background-color: rgba(255, 0, 0, 0.1);
        }

        /* ===== MESSAGE STYLES ===== */
        .message-item {
            margin-bottom: 10px;
            padding: 10px;
            border-radius: 5px;
            background-color: #f9f9f9;
            border-left: 3px solid #ddd;
        }

        .message-error {
            border-left-color: #F44336;
            background-color: rgba(244, 67, 54, 0.05);
        }

        .message-system {
            border-left-color: #2196F3;
            background-color: rgba(33, 150, 243, 0.05);
        }

        .message-robot {
            border-left-color: #4CAF50;
            background-color: rgba(76, 175, 80, 0.05);
        }

        .message-asr {
            border-left-color: #FF9800;
            background-color: rgba(255, 152, 0, 0.05);
        }

        .message-timestamp {
            font-size: 12px;
            color: #888;
            margin-right: 10px;
        }

        .message-sender {
            font-weight: bold;
            margin-right: 10px;
        }

        pre {
            white-space: pre-wrap;
            word-wrap: break-word;
            margin: 5px 0 0 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            background-color: #f5f5f5;
            padding: 5px;
            border-radius: 3px;
        }

        .debug-container {
            margin-top: 30px;
            border-top: 1px solid #ddd;
            padding-top: 20px;
        }

        .chat-container {
            margin-bottom: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .chat-header {
            background-color: #4a76a8;
            color: white;
            padding: 10px 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .chat-header h3 {
            margin: 0;
            color: white;
        }

        .chat-messages {
            height: 400px;
            overflow-y: auto;
            padding: 15px;
            background-color: #f9f9f9;
        }

        .chat-input-container {
            display: flex;
            padding: 10px;
            background-color: white;
            border-top: 1px solid #e0e0e0;
        }

        .chat-input-container input {
            flex: 1;
            margin-right: 10px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

        .chat-input-container button {
            background-color: #4a76a8;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
        }

        .recording-container {
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f5f5f5;
            border-radius: 8px;
            border: 1px solid #e0e0e0;
        }

        #audioPlayer {
            width: 100%;
            margin: 10px 0 20px 0;
        }

        #messageText {
            padding: 10px;
            margin-bottom: 10px;
            background-color: #e8f4fd;
            border-radius: 4px;
            border-left: 3px solid #2196F3;
        }

        /* Chat message styles */
        .chat-message {
            margin-bottom: 15px;
            padding: 10px;
            border-radius: 8px;
            max-width: 80%;
            position: relative;
        }

        .robot-message {
            background-color: #e8f4fd;
            border-left: 3px solid #2196F3;
            margin-right: auto;
        }

        .client-message {
            background-color: #f0f0f0;
            border-right: 3px solid #4CAF50;
            margin-left: auto;
            text-align: right;
        }

        .chat-timestamp {
            font-size: 11px;
            color: #888;
            margin-top: 5px;
        }

        .message-text {
            word-wrap: break-word;
            margin-bottom: 8px;
        }

        .message-media {
            margin-top: 8px;
        }

        .message-media audio {
            width: 100%;
            height: 30px;
        }

        /* Empty state for chat */
        .chat-empty-state {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            color: #888;
            text-align: center;
            padding: 20px;
        }

        .chat-empty-state-icon {
            font-size: 48px;
            margin-bottom: 15px;
        }
    </style>
</head>

<body>
<h1>Robot Conversation Demo</h1>

<button id="connectButton">🔌 Kết nối WebSocket</button>
<div id="status" class="disconnected">Trạng thái: Chưa kết nối</div>

<div id="messageText">Đang chờ tin nhắn...</div>
<audio id="audioPlayer" controls></audio>

<div class="chat-container">
    <div class="chat-header">
        <h3>Cuộc trò chuyện</h3>
    </div>
    <div id="chatMessages" class="chat-messages"></div>
    <div class="chat-input-container">
        <input type="text" id="chatInput" placeholder="Nhập tin nhắn...">
        <button id="chatSendButton">Gửi</button>
    </div>
</div>

<div class="recording-container">
    <h3>Ghi âm</h3>
    <div class="recording-controls">
        <button id="startRecordButton">🎤 Bắt đầu ghi âm</button>
        <button id="stopRecordButton" disabled>⏹️ Dừng ghi âm</button>
    </div>
    <div id="recordingStatus">Sẵn sàng ghi âm</div>
    <div id="uploadProgress" style="display: none;">
        <div></div>
    </div>
</div>

<div class="debug-container">
    <div class="debug-column">
        <h3>Audio Results</h3>
        <div id="audioResults" class="message-column"></div>
    </div>
    <div class="debug-column">
        <h3>Socket Messages</h3>
        <div id="socketMessages" class="message-column"></div>
    </div>
</div>

<script>
    // DOM Elements
    const domElements = {
        // Connection elements
        connectButton: document.getElementById("connectButton"),
        status: document.getElementById("status"),
        responseArea: document.getElementById("responseArea"),
        socketMessages: document.getElementById("socketMessages"),
        audioResults: document.getElementById("audioResults"),
        audioPlayer: document.getElementById("audioPlayer"),
        messageText: document.getElementById("messageText"),

        // Recording elements
        startRecordButton: document.getElementById("startRecordButton"),
        stopRecordButton: document.getElementById("stopRecordButton"),
        recordingStatus: document.getElementById("recordingStatus"),
        uploadProgress: document.getElementById("uploadProgress"),
        uploadProgressBar: document.querySelector("#uploadProgress div"),

        // Chat elements
        chatMessages: document.getElementById("chatMessages"),
        chatInput: document.getElementById("chatInput"),
        chatSendButton: document.getElementById("chatSendButton")
    };

    // Global Variables
    let socket;
    let sessionId = null;

    // Audio recording variables
    let mediaRecorder;
    let audioChunks = [];
    let audioContext;
    let analyser;
    let isRecording = false;
    let recordingStartTime = 0;
    const MIN_RECORDING_TIME = 2000; // Minimum recording time in milliseconds

    // MP3 encoding constants
    const MP3_KBPS = 128;    // Bitrate in kbps
    const MP3_CHANNEL = 1;   // Mono = 1, Stereo = 2
    const MP3_SAMPLE_RATE = 44100; // Sample rate in Hz

    // Message handling functions
    // Add a message to the appropriate message column
    function addMessage(sender, content, type = 'system') {
        // Create message element
        const messageElement = document.createElement('div');
        messageElement.className = 'message-item';

        // Add appropriate class based on sender
        if (sender === 'Error') {
            messageElement.classList.add('message-error');
        } else if (sender === 'System') {
            messageElement.classList.add('message-system');
        } else if (sender === 'Robot') {
            messageElement.classList.add('message-robot');
        } else if (sender === 'ASR') {
            messageElement.classList.add('message-asr');
        }

        // Add timestamp
        const timestamp = new Date().toLocaleTimeString();
        const timestampSpan = document.createElement('span');
        timestampSpan.className = 'message-timestamp';
        timestampSpan.textContent = timestamp;

        // Add sender
        const senderSpan = document.createElement('span');
        senderSpan.className = 'message-sender';
        senderSpan.textContent = sender;

        // Add header with timestamp and sender
        const headerDiv = document.createElement('div');
        headerDiv.appendChild(timestampSpan);
        headerDiv.appendChild(senderSpan);
        messageElement.appendChild(headerDiv);

        // Add content
        const contentPre = document.createElement('pre');
        contentPre.textContent = content;
        messageElement.appendChild(contentPre);

        // Add to appropriate column
        if (type === 'socket') {
            domElements.socketMessages.appendChild(messageElement);
            domElements.socketMessages.scrollTop = domElements.socketMessages.scrollHeight;
        } else if (type === 'audio') {
            domElements.audioResults.appendChild(messageElement);
            domElements.audioResults.scrollTop = domElements.audioResults.scrollHeight;
        }
    }

    // Add a chat message to the chat UI
    function addChatMessage(isRobot, data, conversationId) {
        // Clear empty state if present
        if (domElements.chatMessages.querySelector('.chat-empty-state')) {
            domElements.chatMessages.innerHTML = '';
        }

        const messageDiv = document.createElement('div');
        messageDiv.className = `chat-message ${isRobot ? 'robot-message' : 'client-message'}`;

        // Get current time for timestamp
        const now = new Date();
        const timestamp = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`;

        let messageContent = `<div class="chat-message-content">`;

        // Add message text
        if (isRobot) {
            // Robot message (from socket)
            if (typeof data === 'object' && data !== null) {
                messageContent += `<div class="message-text">${data.text || ''}</div>`;

                // Add audio player if available
                if (data.audio) {
                    messageContent += `
                            <div class="message-media">
                                <audio controls>
                                    <source src="${data.audio}" type="audio/mpeg">
                                    Your browser does not support the audio element.
                                </audio>
                            </div>
                        `;
                }

                // Add emotion if available
                if (data.emotion) {
                    messageContent += `<div class="message-emotion">Cảm xúc: ${data.emotion}</div>`;
                }
            } else {
                messageContent += `<div class="message-text">${data || ''}</div>`;
            }
        } else {
            // Client message (from API)
            if (typeof data === 'object' && data !== null) {
                if (data.text) {
                    messageContent += `<div class="message-text">${data.text}</div>`;
                } else if (data.transcript) {
                    messageContent += `<div class="message-text">${data.transcript}</div>`;
                }

                // Add audio info if available
                if (data.audio_url) {
                    messageContent += `
                            <div class="message-media">
                                <audio controls>
                                    <source src="${data.audio_url}" type="audio/mpeg">
                                    Your browser does not support the audio element.
                                </audio>
                            </div>
                        `;
                }
            } else {
                messageContent += `<div class="message-text">${data || ''}</div>`;
            }
        }

        // Add timestamp
        messageContent += `<div class="chat-timestamp">${timestamp}</div>`;

        // Close message content div
        messageContent += `</div>`;

        // Set the HTML content
        messageDiv.innerHTML = messageContent;

        // Add to chat messages
        domElements.chatMessages.appendChild(messageDiv);

        // Scroll to bottom
        domElements.chatMessages.scrollTop = domElements.chatMessages.scrollHeight;
    }

    // Initialize chat with empty state
    function initializeChat() {
        domElements.chatMessages.innerHTML = `
                <div class="chat-empty-state">
                    <div class="chat-empty-state-icon">💬</div>
                    <p>Kết nối WebSocket và gửi tin nhắn hoặc ghi âm để bắt đầu cuộc trò chuyện</p>
                </div>
            `;
    }

    // Clear chat messages
    function clearChat() {
        domElements.chatMessages.innerHTML = '';
        addMessage("System", "Chat cleared", 'socket');
    }

    // WebSocket Functions
    // Function to get WebSocket URL
    function getWebSocketUrl() {
        const protocol = window.location.protocol === "https:" ? "wss://" : "ws://";
        const host = window.location.host; // This will give you the host (domain + port if present)
        return `${protocol}${host}/ws/free_talk?robot_id=quan_web`; // Adjust the path if necessary
    }

    // Function to connect WebSocket
    function connectWebSocket() {
        try {
            // Create WebSocket connection
            const socketUrl = getWebSocketUrl();
            console.log("Kết nối đến WebSocket:", socketUrl);

            socket = new WebSocket(socketUrl);
            socket.binaryType = "arraybuffer"; // For binary data if needed

            // Connection opened
            socket.addEventListener('open', (event) => {
                domElements.status.textContent = "Trạng thái: Đã kết nối";
                domElements.status.className = "connected";
                domElements.connectButton.textContent = "🔌 Ngắt kết nối WebSocket";
                addMessage("System", "WebSocket đã kết nối. Đang chờ nhận session ID...", 'socket');
            });

            // Listen for messages
            socket.addEventListener('message', (event) => {
                try {
                    const data = event.data;

                    // Try to parse as JSON
                    try {
                        const jsonData = JSON.parse(data);

                        // Store session ID if it's in the message
                        if (jsonData.socket_session_id && !sessionId) {
                            sessionId = jsonData.socket_session_id;
                            console.log("Session ID nhận được:", sessionId);
                            addMessage("System", `Đã nhận session ID: ${sessionId}`, 'socket');
                        }

                        // Process the response
                        handleResponse(jsonData);
                    } catch (jsonError) {
                        // If not JSON, it might be binary data or plain text
                        if (typeof data === 'string') {
                            addMessage("Server", data, 'socket');
                        } else {
                            addMessage("Binary", "Received binary data", 'socket');
                        }
                    }
                } catch (error) {
                    console.error("Error processing message:", error);
                    addMessage("Error", `Error processing message: ${error.message}`, 'socket');
                }
            });

            // Connection closed
            socket.addEventListener('close', (event) => {
                domElements.status.textContent = "Trạng thái: Đã ngắt kết nối";
                domElements.status.className = "disconnected";
                domElements.connectButton.textContent = "🔌 Kết nối WebSocket";

                // Add message with close code and reason
                let closeReason = event.reason ? ` (${event.reason})` : '';
                addMessage("System", `WebSocket đã đóng với mã ${event.code}${closeReason}`, 'socket');

                // Clear session ID
                sessionId = null;
            });

            // Connection error
            socket.addEventListener('error', (error) => {
                console.error("WebSocket error:", error);
                domElements.status.textContent = "Trạng thái: Lỗi kết nối";
                domElements.status.className = "disconnected";
                addMessage("Error", "WebSocket error", 'socket');
            });
        } catch (error) {
            console.error("Error creating WebSocket:", error);
            addMessage("Error", `Error creating WebSocket: ${error.message}`, 'socket');
        }
    }

    // Function to disconnect WebSocket
    function disconnectWebSocket() {
        if (socket) {
            socket.close();
            socket = null;
        }
    }

    // Audio Recording Functions
    // Start audio recording
    async function startRecording() {
        if (!sessionId) {
            alert("Vui lòng kết nối WebSocket trước để nhận session ID. Không thể bắt đầu ghi âm mà không có session ID.");
            return;
        }

        try {
            // Request microphone access
            const stream = await navigator.mediaDevices.getUserMedia({audio: true});

            // Set up media recorder with higher bit rate for better quality
            const options = {
                mimeType: 'audio/webm;codecs=opus',
                audioBitsPerSecond: 128000
            };

            mediaRecorder = new MediaRecorder(stream, options);

            // Start recording
            audioChunks = [];
            mediaRecorder.start();
            isRecording = true;
            recordingStartTime = Date.now();

            // Update UI
            domElements.startRecordButton.disabled = true;
            domElements.stopRecordButton.disabled = false;
            domElements.recordingStatus.innerHTML = '<span class="recording-indicator"></span> Đang ghi âm...';

            // Listen for data available event
            mediaRecorder.addEventListener("dataavailable", event => {
                if (event.data.size > 0) {
                    audioChunks.push(event.data);
                }
            });

            // Listen for recording stopped event
            mediaRecorder.addEventListener("stop", () => {
                // Stop all tracks in the stream
                stream.getTracks().forEach(track => track.stop());

                // Calculate recording duration
                const recordingDuration = Date.now() - recordingStartTime;

                // Check if recording is too short
                if (recordingDuration < MIN_RECORDING_TIME) {
                    domElements.recordingStatus.textContent = "Ghi âm quá ngắn. Vui lòng ghi âm ít nhất 2 giây.";
                    resetRecordingUI();
                    return;
                }

                // Process the recorded audio
                if (audioChunks.length > 0) {
                    uploadRecordedAudio();
                } else {
                    domElements.recordingStatus.textContent = "Không có dữ liệu âm thanh được ghi lại";
                    resetRecordingUI();
                }
            });

            // Add message to the socket messages column
            addMessage("System", `Bắt đầu ghi âm với session ID: ${sessionId}`, 'socket');

        } catch (error) {
            console.error("Error starting recording:", error);
            domElements.recordingStatus.textContent = `Lỗi: ${error.message}`;
            addMessage("Error", `Error starting recording: ${error.message}`, 'socket');
            resetRecordingUI();
        }
    }

    // Stop audio recording
    function stopRecording() {
        if (!mediaRecorder || mediaRecorder.state === 'inactive') {
            return;
        }

        // Check if minimum recording time has elapsed
        const recordingTime = Date.now() - recordingStartTime;
        if (recordingTime < MIN_RECORDING_TIME) {
            const remainingTime = Math.ceil((MIN_RECORDING_TIME - recordingTime) / 1000);
            domElements.recordingStatus.textContent = `Vui lòng đợi thêm ${remainingTime} giây...`;

            // Wait for minimum time before stopping
            setTimeout(() => {
                mediaRecorder.stop();
                isRecording = false;
                domElements.recordingStatus.textContent = "Đang xử lý âm thanh...";
            }, MIN_RECORDING_TIME - recordingTime);
        } else {
            // Stop recording immediately
            mediaRecorder.stop();
            isRecording = false;
            domElements.recordingStatus.textContent = "Đang xử lý âm thanh...";
        }
    }

    // Reset recording UI elements
    function resetRecordingUI() {
        domElements.startRecordButton.disabled = false;
        domElements.stopRecordButton.disabled = true;
        domElements.uploadProgress.style.display = "none";
        domElements.uploadProgressBar.style.width = "0%";
    }

    // Upload recorded audio to server
    async function uploadRecordedAudio() {
        try {
            // Create audio blob from chunks
            const audioBlob = new Blob(audioChunks, {type: 'audio/webm;codecs=opus'});

            // Convert to MP3 for better compatibility
            domElements.recordingStatus.textContent = "Đang chuyển đổi sang MP3...";

            try {
                const mp3Blob = await convertToMP3(audioBlob);
                sendAudioToServer(mp3Blob, 'audio/mp3');
            } catch (error) {
                console.error("Error converting to MP3:", error);
                // Fallback to original format if MP3 conversion fails
                addMessage("Error", `MP3 conversion failed: ${error.message}. Using original format.`, 'socket');
                sendAudioToServer(audioBlob, 'audio/webm');
            }
        } catch (error) {
            console.error("Error uploading audio:", error);
            domElements.recordingStatus.textContent = `Lỗi: ${error.message}`;
            addMessage("Error", `Error uploading audio: ${error.message}`, 'socket');
            resetRecordingUI();
        }
    }

    // Send audio to server via API endpoint
    function sendAudioToServer(audioBlob, mimeType) {
        if (!sessionId) {
            alert("Không có session ID. Vui lòng kết nối WebSocket trước.");
            resetRecordingUI();
            return;
        }

        // Show upload progress
        domElements.uploadProgress.style.display = "block";

        // Create FormData object to send the file
        const formData = new FormData();

        // Create a file from the blob with a proper filename and extension
        const extension = mimeType === 'audio/mp3' ? 'mp3' : 'webm';
        const filename = `audio_${new Date().getTime()}.${extension}`;
        const audioFile = new File([audioBlob], filename, {type: mimeType});

        // Add file to FormData
        formData.append('audioFile', audioFile);

        // Create API URL with socket_session_id parameter
        const apiUrl = `/api/robot/conversation/sessions/${sessionId}/process-audio`;

        // Update UI
        domElements.recordingStatus.textContent = "Đang gửi audio...";

        // Log the request details
        addMessage("System", `Gửi audio tới: ${apiUrl}`, 'socket');
        addMessage("System", `Session ID: ${sessionId}`, 'socket');

        // Send the request
        fetch(apiUrl, {
            method: 'POST',
            body: formData
        })
            .then(response => {
                // Update progress bar
                domElements.uploadProgressBar.style.width = "100%";

                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                // Handle successful response
                domElements.recordingStatus.textContent = "Âm thanh đã được gửi, đang chờ phản hồi...";
                addMessage("System", `Đã gửi audio (${(audioBlob.size / 1024).toFixed(2)} KB)`, 'socket');
                addMessage("API-Response", JSON.stringify(data, null, 2), 'socket');

                // Hiển thị phản hồi từ API trong cuộc trò chuyện (ở phía bên phải)
                if (data) {
                    if (data.type === "ACTIVE_RECORD") {
                        // Xử lý response format mới
                        if (data.text) {
                            addChatMessage(false, {text: data.text});
                        } else {
                            addChatMessage(false, {text: "Đã gửi audio thành công"});
                        }

                        // Nếu có audio_url, có thể xử lý thêm ở đây nếu cần
                        if (data.audio_url) {
                            console.log("Received audio URL:", data.audio_url);
                            // Có thể thêm code để xử lý audio_url nếu cần
                        }
                    } else if (data.data) {
                        // Xử lý response format cũ
                        if (data.data.transcript) {
                            addChatMessage(false, {text: data.data.transcript});
                        } else if (data.data.text) {
                            addChatMessage(false, {text: data.data.text});
                        } else {
                            addChatMessage(false, {text: "Đã gửi audio thành công"});
                        }
                    } else {
                        // Nếu không có dữ liệu, hiển thị thông báo chung
                        addChatMessage(false, {text: "Đã gửi audio thành công"});
                    }
                } else {
                    // Nếu không có dữ liệu, hiển thị thông báo chung
                    addChatMessage(false, {text: "Đã gửi audio thành công"});
                }

                socket.send(JSON.stringify(data));

                // Reset recording UI after a delay
                setTimeout(resetRecordingUI, 2000);
            })
            .catch(error => {
                console.error("Error sending audio via API:", error);
                domElements.recordingStatus.textContent = `Lỗi: ${error.message}`;
                addMessage("Error", `Error sending audio via API: ${error.message}`, 'socket');
                resetRecordingUI();
            });
    }

    // Convert audio blob to MP3 format
    function convertToMP3(audioBlob) {
        return new Promise(async (resolve, reject) => {
            try {
                // Check if lamejs is available
                if (typeof lamejs === 'undefined') {
                    reject(new Error("MP3 encoding library not loaded"));
                    return;
                }

                // Create an audio element to decode the audio
                const audioElement = new Audio();
                audioElement.src = URL.createObjectURL(audioBlob);

                // Create an AudioContext to decode the audio
                const audioContext = new (window.AudioContext || window.webkitAudioContext)();

                // When audio is loaded, decode it
                audioElement.onloadedmetadata = async () => {
                    try {
                        // Get the audio data as an array buffer
                        const arrayBuffer = await fetch(audioElement.src).then(res => res.arrayBuffer());

                        // Decode the audio data
                        const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);

                        // Get the raw PCM data
                        const leftChannel = audioBuffer.getChannelData(0);

                        // Create MP3 encoder
                        const mp3encoder = new lamejs.Mp3Encoder(MP3_CHANNEL, MP3_SAMPLE_RATE, MP3_KBPS);

                        // Process the audio in chunks
                        const sampleBlockSize = 1152; // Must be a multiple of 576 for mono, 1152 for stereo
                        const mp3Data = [];

                        // Convert float32 to int16
                        const samples = new Int16Array(leftChannel.length);
                        for (let i = 0; i < leftChannel.length; i++) {
                            // Convert from [-1, 1] to [-32768, 32767]
                            samples[i] = Math.max(-1, Math.min(1, leftChannel[i])) * 0x7FFF;
                        }

                        // Encode to MP3
                        for (let i = 0; i < samples.length; i += sampleBlockSize) {
                            const sampleChunk = samples.subarray(i, i + sampleBlockSize);
                            const mp3buf = mp3encoder.encodeBuffer(sampleChunk);
                            if (mp3buf.length > 0) {
                                mp3Data.push(mp3buf);
                            }
                        }

                        // Get the final part of the MP3
                        const mp3buf = mp3encoder.flush();
                        if (mp3buf.length > 0) {
                            mp3Data.push(mp3buf);
                        }

                        // Create a Blob from the MP3 data
                        const mp3Blob = new Blob(mp3Data, {type: 'audio/mp3'});

                        // Clean up
                        URL.revokeObjectURL(audioElement.src);

                        // Log success
                        addMessage("System", `MP3 conversion successful: ${(mp3Blob.size / 1024).toFixed(2)} KB`, 'audio');

                        resolve(mp3Blob);
                    } catch (err) {
                        reject(err);
                    }
                };

                audioElement.onerror = (err) => {
                    reject(new Error("Error loading audio: " + err));
                };

            } catch (err) {
                reject(err);
            }
        });
    }

    // Message Handling Functions
    // Function to handle response from server
    function handleResponse(response) {
        if (!response || typeof response !== 'object') {
            throw new Error("Invalid response format");
        }

        // Log the raw response for debugging
        console.log("Processing response:", response);
        console.log("Response type:", response.type);
        console.log("Response data type:", typeof response.data);
        console.log("Is data an array:", Array.isArray(response.data));

        try {
            // Handle different response types
            if (response.type === "CHAT_RESPONSE" && response.data) {
                handleChatResponse(response);

                // Add conversation ID if available
                if (response.conversation_id) {
                    addMessage("Conversation", `ID: ${response.conversation_id}`, 'audio');
                }
            } else if (response.type === "ASR" && response.data) {
                handleAsrResponse(response);
            } else if (response.type === "CHAT_STALLING") {
                // Handle stalling messages
                if (Array.isArray(response.data) && response.data.length > 0) {
                    const stallingMessage = response.data[0];
                    if (stallingMessage.text) {
                        addMessage("Robot-Stalling", stallingMessage.text, 'socket');
                        // Hiển thị trong box cuộc trò chuyện bên trái (isRobot = true)
                        addChatMessage(true, {text: stallingMessage.text});
                    }
                } else if (response.data && response.data.text) {
                    addMessage("Robot-Stalling", response.data.text, 'socket');
                    // Hiển thị trong box cuộc trò chuyện bên trái (isRobot = true)
                    addChatMessage(true, {text: response.data.text});
                } else if (response.data && response.data.messages && Array.isArray(response.data.messages) && response.data.messages.length > 0) {
                    // Format mới với messages array
                    const stallingMessage = response.data.messages[0];
                    if (stallingMessage.text) {
                        addMessage("Robot-Stalling", stallingMessage.text, 'socket');
                        // Hiển thị trong box cuộc trò chuyện bên trái (isRobot = true)
                        addChatMessage(true, {text: stallingMessage.text});
                    }
                } else {
                    addMessage("Robot-Stalling", JSON.stringify(response.data), 'socket');
                    // Hiển thị thông báo chung trong box cuộc trò chuyện bên trái (isRobot = true)
                    addChatMessage(true, {text: "Robot đang xử lý..."});
                }
            } else {
                // Handle any other response type
                addMessage("Unknown-Response", JSON.stringify(response, null, 2), 'socket');
            }
        } catch (error) {
            console.error("Error in handleResponse:", error);
            addMessage("Error", `Error processing response: ${error.message}`, 'socket');
            addMessage("ErrorData", JSON.stringify(response, null, 2), 'socket');
        }
    }

    // Handle chat response from server
    function handleChatResponse(response) {
        if (!response.data) {
            addMessage("Error", "Chat response missing data field", 'socket');
            return;
        }

        // Save socket_session_id if available
        if (response.socket_session_id && !sessionId) {
            sessionId = response.socket_session_id;
            addMessage("System", `Đã nhận session ID: ${sessionId}`, 'socket');
        }

        // Add the full response to the socket messages column for debugging
        addMessage("Server-Response", JSON.stringify(response, null, 2), 'socket');

        // Handle the data based on its type
        if (Array.isArray(response.data)) {
            // If data is an array, process each message
            console.log("Processing data as array");
            response.data.forEach(message => {
                processMessage(message);
            });

            // Add all messages to chat UI
            response.data.forEach(message => {
                addChatMessage(true, message, response.conversation_id);
            });
        } else if (response.data.messages && Array.isArray(response.data.messages)) {
            // If data has a messages array property
            console.log("Processing data.messages as array");
            response.data.messages.forEach(message => {
                processMessage(message);
            });

            // Add all messages to chat UI
            response.data.messages.forEach(message => {
                addChatMessage(true, message, response.conversation_id);
            });
        } else {
            // If data is a single message object
            console.log("Processing data as single object");
            processMessage(response.data);

            // Add to chat UI
            addChatMessage(true, response.data, response.conversation_id);
        }
    }

    // Handle ASR response from server
    function handleAsrResponse(response) {
        if (!response.data) {
            addMessage("Error", "ASR response missing data field", 'socket');
            return;
        }

        if (response.data.transcript) {
            addMessage("ASR", `Bản transcribe: ${response.data.transcript}`, 'socket');
            // Add to chat UI
            addChatMessage(false, {text: response.data.transcript}, response.conversation_id);
        } else if (response.data.text) {
            addMessage("ASR", `Bản transcribe: ${response.data.text}`, 'socket');
            // Add to chat UI
            addChatMessage(false, response.data, response.conversation_id);
        } else {
            addMessage("ASR", `Received ASR data: ${JSON.stringify(response.data)}`, 'socket');
            // Add to chat UI
            addChatMessage(false, response.data, response.conversation_id);
        }
    }

    // Process individual message from response
    function processMessage(message) {
        if (!message) return;
        console.log("Processing message:", message);

        // Display text if available
        if (message.text) {
            domElements.messageText.textContent = message.text;
            addMessage("Robot", message.text, 'socket');
        }

        // Play audio if available
        if (message.audio) {
            playAudio(message.audio);
        }

        // Handle emotion if available
        if (message.emotion) {
            addMessage("Robot-Emotion", `Emotion: ${message.emotion}`, 'audio');
        }

        // Handle animations if available
        if (message.animations && message.animations.length > 0) {
            addMessage("Robot-Animations", `Animations: ${JSON.stringify(message.animations)}`, 'audio');
        }
    }

    // Play audio from URL
    function playAudio(audioUrl) {
        if (!audioUrl) return;

        domElements.audioPlayer.src = audioUrl;

        // Play with error handling
        domElements.audioPlayer.play().catch(error => {
            console.error("Error playing audio:", error);
            addMessage("Error", `Failed to play audio: ${error.message}`, 'audio');
        });

        addMessage("Robot-Audio", `Playing audio: ${audioUrl}`, 'audio');
    }

    // Send text message via WebSocket
    function sendChatMessage() {
        if (!socket || socket.readyState !== WebSocket.OPEN) {
            alert("WebSocket không kết nối. Vui lòng kết nối lại.");
            return;
        }

        if (!sessionId) {
            alert("Không có session ID. Vui lòng kết nối WebSocket trước.");
            return;
        }

        const messageText = domElements.chatInput.value.trim();
        if (!messageText) {
            return; // Don't send empty messages
        }

        try {
            // Create message object
            const message = {
                type: "ACTIVE_RECORD",
                socket_session_id: sessionId, // Use socket_session_id instead of session_id
                text: messageText
            };

            // Send message
            socket.send(JSON.stringify(message));

            // Add message to chat UI
            addChatMessage(false, {text: messageText});

            // Add to debug log
            addMessage("User", `Sent: ${messageText}`, 'socket');

            // Clear input
            domElements.chatInput.value = '';

        } catch (error) {
            console.error("Error sending chat message:", error);
            addMessage("Error", `Error sending message: ${error.message}`, 'socket');
        }
    }

    // Handle chat input keypress (Enter to send)
    function handleChatInputKeypress(event) {
        if (event.key === 'Enter' && !event.shiftKey) {
            event.preventDefault(); // Prevent default to avoid newline
            sendChatMessage();
        }
    }

    // Event Listeners
    // WebSocket connection button
    domElements.connectButton.addEventListener("click", () => {
        if (socket && socket.readyState === WebSocket.OPEN) {
            disconnectWebSocket();
        } else {
            connectWebSocket();
        }
    });

    // Recording buttons
    domElements.startRecordButton.addEventListener("click", startRecording);
    domElements.stopRecordButton.addEventListener("click", stopRecording);

    // Chat input event listeners
    domElements.chatInput.addEventListener("keypress", handleChatInputKeypress);
    domElements.chatSendButton.addEventListener("click", sendChatMessage);

    // Initialize the application
    function initializeApp() {
        // Auto-connect WebSocket
        setTimeout(() => {
            if (!socket || socket.readyState !== WebSocket.OPEN) {
                connectWebSocket();
            }
        }, 1000);

        // Set up audio player event listeners
        domElements.audioPlayer.addEventListener('play', () => {
            addMessage("Audio", "Started playing audio", 'audio');
        });

        domElements.audioPlayer.addEventListener('ended', () => {
            addMessage("Audio", "Finished playing audio", 'audio');
        });

        domElements.audioPlayer.addEventListener('error', (e) => {
            addMessage("Error", `Audio playback error: ${e.target.error?.message || 'Unknown error'}`, 'audio');
        });

        // Initialize chat area
        initializeChat();
    }

    // Call initialization function
    initializeApp();
</script>
</body>

</html>